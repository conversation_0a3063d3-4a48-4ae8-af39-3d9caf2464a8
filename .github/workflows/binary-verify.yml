name: Validate Binaries

on:
  workflow_dispatch:
    inputs:
      version:
        description: "The published version, like \"0.2.0\""
        type: string
        required: true
      docker-rc:
        description: "-rc or not"
        type: boolean
        default: false
  workflow_call:
    inputs:
      version:
        description: "The published version, like \"0.2.0\""
        type: string
        required: true
      docker-rc:
        description: "-rc or not"
        type: boolean
        required: true

env:
  VERSION: "${{ inputs.version }}"
  RELEASE_TAG: "v${{ inputs.version }}"
permissions:
  contents: write # don't actually need to write, but this lets us see draft releases

jobs:
  list-targets:
    uses: ./.github/workflows/list-targets.yml

  dispatch-targets:
    needs: list-targets
    runs-on: ubuntu-latest
    outputs:
      windows: ${{ steps.run.outputs.windows }}
      non-windows: ${{ steps.run.outputs.non-windows }}
    steps:
      - name: Run
        id: run
        run: |
          set -x
          echo "windows=$(<<<"$VALIDATE_BY_TARGET" jq -c 'to_entries | map(select(.value == "windows")) | map(.key)')" >> "$GITHUB_OUTPUT"
          echo "non-windows=$(<<<"$VALIDATE_BY_TARGET" jq -c 'to_entries | map(select(.value != "windows")) | map(.key)')" >> "$GITHUB_OUTPUT"
        env:
          VALIDATE_BY_TARGET: ${{ needs.list-targets.outputs.validate_by_target }}
          BUILD_BY_TARGET: ${{ needs.list-targets.outputs.build_by_target }}


  full-coverage:
    needs: list-targets
    runs-on: ubuntu-latest
    steps:

      - name: Get asset names
        run: >
          gh release -R ${{ github.repository }} view "$RELEASE_TAG" --json assets | jq -r '.assets | map(.name) | .[]'
          | sort 
          | sed -E 's/\.(zip|tar\.gz)$//'
          | sort
          > from-release.txt
        env:
          GH_TOKEN: ${{ github.token }}

      - name: Get target names
        run: <<<"$TARGET_NAMES" jq -r '.[] | "mdq-\(.)"' > from-list-targets.txt
        env:
          TARGET_NAMES: ${{ needs.list-targets.outputs.names }}

      - name: Validate that they're the same
        run: diff -y from-list-targets.txt from-release.txt


  unix-like:
    needs: [ list-targets, dispatch-targets ]
    strategy:
      matrix:
        target: ${{ fromJSON(needs.dispatch-targets.outputs.non-windows) }}
    runs-on: ${{ fromJSON(needs.list-targets.outputs.validate_by_target)[matrix.target] }}-latest
    steps:
      - name: Download tarball
        run: gh release -R ${{ github.repository }} download "$RELEASE_TAG" -p mdq-${{ matrix.target }}.tar.gz
        env:
          GH_TOKEN: ${{ github.token }}

      - name: Expand
        run: tar xzvf mdq-${{ matrix.target }}.tar.gz

      - name: Run --version
        id: mdq-version
        run: |
          set -euo pipefail
          mdq_output="$(./mdq --version)"
          echo "version-b64=$(base64 <<<"$mdq_output")" >> "$GITHUB_OUTPUT"

      - name: Verify version
        run:
          diff -y <(echo "mdq $VERSION") <(base64 -d <<<"$ACTUAL_VERSION")
        env:
          ACTUAL_VERSION: ${{ steps.mdq-version.outputs.version-b64 }}


  windows:
    needs: [ list-targets, dispatch-targets ]
    runs-on: ${{ fromJSON(needs.list-targets.outputs.validate_by_target)[matrix.target] }}-latest
    strategy:
      matrix:
        target: ${{ fromJSON(needs.dispatch-targets.outputs.windows) }}
    steps:

      - name: Download zip
        run: gh release -R ${{ github.repository }} download ${{ env.RELEASE_TAG}} -p mdq-${{ matrix.target }}.zip
        env:
          GH_TOKEN: ${{ github.token }}

      - name: Expand
        run: Expand-Archive mdq-${{ matrix.target }}.zip

      # Use JSON, because base64 is a pain in powershell
      - name: Run --version
        id: mdq-version
        run: |
          $version_json = .\mdq-${{ matrix.target }}\mdq.exe --version | ConvertTo-Json
          echo "version_json=$version_json" > $env:GITHUB_OUTPUT

      - name: Verify version
        run: |
          $diff = Compare-Object -CaseSensitive (echo "mdq $env:VERSION" | ConvertTo-Json) $env:ACTUAL
          if ($diff) {
            Write-Output "Difference found:"
            $diff | Format-Table
            exit 1
          }
        env:
          ACTUAL: ${{ steps.mdq-version.outputs.version_json }}

  docker:
    runs-on: ubuntu-latest
    steps:
      - name: Calculate tag
        id: tag
        run: |
          tag="${VERSION}"
          if [[ ${{ inputs.docker-rc }} == true ]]; then
            tag="${VERSION}-rc"
          fi
          echo "id=$tag" >> "$GITHUB_OUTPUT"

      - name: Pull tag
        run: docker pull "yshavit/mdq:$DOCKER_TAG"
        env:
          DOCKER_TAG: ${{ steps.tag.outputs.id }}

      - name: Run --version
        id: mdq-version
        run: |
          mdq_output="$(docker run --rm -i "yshavit/mdq:$DOCKER_TAG" --version)"
          echo "version-b64=$(base64 <<<"$mdq_output")" >> "$GITHUB_OUTPUT"
        env:
          DOCKER_TAG: ${{ steps.tag.outputs.id }}

      - name: Verify version
        run:
          diff -y <(echo "mdq $VERSION") <(base64 -d <<<"$ACTUAL_VERSION")
        env:
          ACTUAL_VERSION: ${{ steps.mdq-version.outputs.version-b64 }}


  attestations:
    runs-on: ubuntu-latest
    steps:
      - name: Download zip
        run: gh release -R ${{ github.repository }} download "$RELEASE_TAG"
        env:
          GH_TOKEN: ${{ github.token }}

      - name: Attestations
        run: |
          set -euo pipefail
          for z_file in *.zip; do
            echo "::group::$z_file"
            z_dir="${z_file}.dir"
            mkdir "$z_dir"
            unzip "$z_file" -d "$z_dir"
            echo "Will verify: " "$z_dir"/*
            gh attestation verify -o ${{ github.repository_owner }} "$z_dir"/*
            echo '::endgroup::'
          done
        env:
          GH_TOKEN: ${{ github.token }}
