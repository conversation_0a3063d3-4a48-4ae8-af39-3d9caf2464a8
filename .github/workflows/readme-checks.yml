name: Documentation

on:
  push:
    branches: [ "main" ]
  workflow_dispatch: { }

env:
  CARGO_TERM_COLOR: always

jobs:
  check-msrv:
    runs-on: ubuntu-latest
    steps:

      - uses: actions/checkout@v4

      - run: docker pull yshavit/mdq

      - name: pull cargo-msrv from docker hub
        run: docker pull foresterre/cargo-msrv

      - name: find minimum supported rust version
        id: run-msrv
        run: |
          set -euo pipefail
          min_version="$(docker run --rm -t -v "$PWD/":/app/ foresterre/cargo-msrv find --no-log --output-format minimal | tr -d $'\r\n')"
          echo "::notice title=cargo-msrv::$min_version"
          echo "result=$min_version" >> "$GITHUB_OUTPUT"

      - name: check versions in readme
        id: readme-version
        run: |
          set -euo pipefail
          exit_code=0
          while IFS=: read -r file line_no version_contents; do
            found_rustc_version="${version_contents//rustc >= /}"
            if [[ "$found_rustc_version" == "$MSRV_VERSION" ]]; then
              echo "::notice file=$file,line=$line_no,title=version::✅ $found_rustc_version"
            else
              echo "::error file=$file,line=$line_no,title=version::$found_rustc_version should have been $MSRV_VERSION"
              exit_code=1
            fi
          done <<<"$(grep -HnoE 'rustc >= \S+' README.md)"
          exit "$exit_code"
        env:
          MSRV_VERSION: ${{ steps.run-msrv.outputs.result }}

      - name: check version in Cargo.toml
        run: |
          msrv_toml="$(cargo metadata --no-deps --format-version 1 | jq -r '.packages[].rust_version')"
          if [[ "$msrv_toml" != "$MSRV_VERSION" ]]; then
            grep -n '^rust-version =' Cargo.toml | while read -r line; do
              line_no="$(<<<"$line" cut -f1 -d:)"
              echo "::error file=Cargo.toml,line=$line_no,title=Cargo.toml::rust-version should have been $MSRV_VERSION"
            done
            exit 1
          fi
        env:
          MSRV_VERSION: ${{ steps.run-msrv.outputs.result }}


  
  build-crate-docs:
    runs-on: ubuntu-latest
    steps:

      - name: Checkout
        uses: actions/checkout@v4

      - name: Build docs
        run: cargo doc --no-deps

      - name: Add redirect page
        run: cp .github/gha_assets/crate-doc-root-redirect.html target/doc/index.html

      - name: Upload artifact
        uses: actions/upload-pages-artifact@v3
        with:
          path: target/doc

  deploy-crate-docs:
    needs: build-crate-docs
    runs-on: ubuntu-latest
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    concurrency:
      group: "pages"
      cancel-in-progress: false
    permissions:
      contents: read
      pages: write
      id-token: write
    steps:

      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4
