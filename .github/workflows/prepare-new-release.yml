name: "Release: (01) Prepare"

on:
  workflow_dispatch:
    inputs:
      version_to_release:
        description: 'version to cut a release as (e.g. `0.2.0`)'
        required: true
        type: string
      base_ref:
        description: 'the git ref to go against (e.g. `main`)'
        required: false
        type: string
        default: main
      base_ref_version:
        description: 'verify current dev version, w/o "-dev" suffix (e.g. `0.1.0`)'
        required: true
        type: string
      create_pr:
        description: whether to create the PR for the release
        required: false
        type: boolean
        default: true

jobs:
  create_draft:
    runs-on: ubuntu-latest
    env:
      GH_TOKEN: ${{ github.token }}
      RELEASE_VERSION: ${{ github.event.inputs.version_to_release }}
      TAG_NAME: "v${{ github.event.inputs.version_to_release }}"
    permissions:
      contents: write
      pull-requests: write

    steps:

      - name: Configure git
        run: |
          set -euo pipefail
          git config --global user.name 'github-actions[bot]'
          git config --global user.email 'github-actions[bot]@users.noreply.github.com'

      - uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.base_ref }}

      - name: Check that tag is available
        run: |
          set -euo pipefail
          if gh release view "$TAG_NAME" ; then
            echo "::error ::tag $TAG_NAME already exists"
            exit 1
          fi

      - name: Check Cargo.toml version
        run: |
          set -euo pipefail
          toml_current_version=$(grep '^version' Cargo.toml | sed 's/version = "\(.*\)"/\1/')
          expect_version="${VERIFY_VERSION}-dev"
          if [[ "$toml_current_version" != "$expect_version" ]]; then
            echo "::error title=bad version::Expected version $expect_version does not match current version $toml_current_version."
            exit 1
          fi
        env:
          VERIFY_VERSION: ${{ github.event.inputs.base_ref_version }}

      - name: Update Cargo.toml
        run: |
          set -euo pipefail
          sed -i 's/^version = ".*"/version = "${{ env.RELEASE_VERSION }}"/' Cargo.toml
          cargo metadata >/dev/null

      - name: Commit change
        run: git commit -am "bump version to $RELEASE_VERSION"

      - name: Push to branch
        run: |
          set -euo pipefail
          git checkout -b "pending-releases/$RELEASE_VERSION"
          git push --set-upstream origin "pending-releases/$RELEASE_VERSION"

      - name: Create new release
        id: create_release
        run: |
          gh release create "$TAG_NAME" --draft --title "$TAG_NAME" --generate-notes --target "pending-releases/$RELEASE_VERSION"

  upload_assets:
    needs: create_draft
    uses: ./.github/workflows/release-assets.yml
    permissions:
      id-token: write
      attestations: write
      contents: write
    secrets: inherit
    with:
      branch_name: "pending-releases/${{ github.event.inputs.version_to_release }}"
      target_branch_name: ${{ github.event.inputs.base_ref }}

  open_pr:
    if: inputs.create_pr
    runs-on: ubuntu-latest
    env:
      GH_TOKEN: ${{ github.token }}
      RELEASE_VERSION: ${{ github.event.inputs.version_to_release }}
    permissions:
      pull-requests: write
    needs: [ create_draft, upload_assets ]
    steps:

      - uses: actions/checkout@v4
        with:
          ref: pending-releases/${{ env.RELEASE_VERSION }}

      - name: Open PR
        run: |
          set -euo pipefail

          body_text="$(<.github/gha_assets/release_pr_template.md sed "s/{{RELEASE_VERSION}}/$RELEASE_VERSION/g")"
          gh pr create --title "Release v${RELEASE_VERSION}" --body "$body_text" --base "$TARGET_REF"
        env:
          TARGET_REF: ${{ github.event.inputs.base_ref }}
