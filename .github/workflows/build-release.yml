name: Binaries
on:
  push:
    branches: [ "main" ]
  workflow_dispatch:
    inputs:
      docker-tag-type:
        description: The docker tag to upload as
        required: true
        default: none
        type: choice
        options:
          - latest
          - stable
          - rc
          - none
  workflow_call:
    inputs:
      docker-tag-type:
        description: The docker tag to upload as
        required: true
        type: string
      branch_name:
        description: The branch to build against
        required: true
        type: string

env:
  CARGO_TERM_COLOR: always

# for attestations
permissions:
  id-token: write
  attestations: write
  contents: read

jobs:
  docker:
    if: ${{ inputs.docker-tag-type != 'none' }}
    environment: Docker Hub
    runs-on: ubuntu-latest
    steps:

      - uses: actions/checkout@v4
        with:
          ref: ${{ inputs.branch_name }}

      - name: get tag
        id: get-tag
        run: |
          if [[ "${DOCKER_TAG_TYPE}" = 'stable' || "${DOCKER_TAG_TYPE}" = 'rc' ]]; then
            tag="$(cargo metadata --format-version 1 | jq -r '.packages[] | select(.name == "mdq") | .version')"
            if [[ "${DOCKER_TAG_TYPE}" = 'rc' ]]; then
              tag="$tag-rc"
            fi
          else
            tag=latest
          fi
          echo "tag=$tag" >> "$GITHUB_OUTPUT"
        env:
          DOCKER_TAG_TYPE: ${{ inputs.docker-tag-type }}
          DOCKER_USERNAME: ${{ vars.DOCKERHUB_USERNAME }}

      - name: Log in to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ vars.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_PAT }}

      - name: Gather metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ vars.DOCKERHUB_USERNAME }}/mdq

      - name: Build and push
        uses: docker/build-push-action@v6
        with:
          context: .
          push: true
          tags: ${{ vars.DOCKERHUB_USERNAME}}/mdq:${{ steps.get-tag.outputs.tag }}
          labels: ${{ steps.meta.outputs.labels }}

  build-targets:
    uses: ./.github/workflows/list-targets.yml

  build:
    needs: build-targets
    strategy:
      matrix:
        target: ${{ fromJSON(needs.build-targets.outputs.names) }}
    runs-on: ${{ fromJSON(needs.build-targets.outputs.build_by_target)[matrix.target] }}-latest
    env:
      BUILD_TARGET: ${{ fromJSON(needs.build-targets.outputs.rust_target_by_target)[matrix.target] }}
    steps:

      - name: Pick file name
        shell: bash
        run: |
          if [[ "$BUILD_TARGET" == *-windows-* ]]; then
            build_file_name=mdq.exe
          else
            build_file_name=mdq
          fi
          echo "BUILD_FILE_NAME=${build_file_name}" >> "$GITHUB_ENV"
          echo "BUILD_FILE_PATH=target/$BUILD_TARGET/release/$build_file_name" >> "$GITHUB_ENV"

      - name: rustc version
        run: rustc --version --verbose

      - uses: actions/checkout@v4
        with:
          ref: ${{ inputs.branch_name }}

      - name: "Cache cargo"
        id: cache-cargo
        uses: "actions/cache@v4"
        with:
          path: |
            ~/.cargo/bin/
            ~/.cargo/registry/index/
            ~/.cargo/registry/cache/
            ~/.cargo/git/db/
            target/
          key: ${{ runner.os }}-${{ matrix.target }}-cargo-${{ hashFiles('**/Cargo.lock') }}

      - name: install cross
        run: command -v cross || cargo install cross --git https://github.com/cross-rs/cross
        if: runner.os != 'macOS'
        working-directory: ${{ runner.temp }}
        env:
          GH_TOKEN: ${{ github.token }}

      - name: build
        run: |
          set -euo pipefail
          if [[ "$RUNNER_OS" = macOS ]]; then
            build_bin=cargo
          else
            build_bin=cross
          fi
          "$build_bin" build --release --target "$BUILD_TARGET"
        env:
          RUNNER_OS: ${{ runner.os }}

      - name: check for any changes in the git tree
        shell: bash
        run: |
          set -euo pipefail
          if [[ -n "$(git status --porcelain)" ]]; then
            echo '::error title=post-build check::changes detected in git tree'
            git status
            exit 1
          fi

      - name: chmod
        if: "!contains(matrix.target, 'windows')"
        run: chmod +x "$BUILD_FILE_PATH"

      - name: Attest Build Provenance
        uses: actions/attest-build-provenance@v2
        with:
          subject-path: ${{ env.BUILD_FILE_PATH }}

      - name: upload binary
        uses: actions/upload-artifact@v4
        with:
          if-no-files-found: error
          name: mdq-${{ matrix.target }}
          path: ${{ env.BUILD_FILE_PATH }}
