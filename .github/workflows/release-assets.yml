name: "Release: (02) Assets"
on:
  pull_request:
    types: [ "synchronize" ]
    branches: [ "main" ]
    paths:
      - Cargo.toml
  workflow_dispatch:
    inputs:
      branch_name:
        description: "The branch to monitor"
        type: string
        required: true
  workflow_call:
    inputs:
      branch_name:
        description: "The branch to monitor"
        type: string
        required: true
      target_branch_name:
        description: "The branch that will be merged into; if not provided, will fetch from branch_name's base"
        type: string
        required: false

# for attestations in build-release.yml
permissions:
  id-token: write
  attestations: write
  contents: write # needed for viewing and uploading to the draft release

jobs:

  validate:
    if: startsWith(inputs.branch_name || github.head_ref, 'pending-releases/')
    env:
      GH_TOKEN: ${{ github.token }}
      BRANCH_NAME: ${{ inputs.branch_name || github.head_ref }}
      TARGET_BRANCH: ${{ inputs.target_branch_name }}
    runs-on: ubuntu-latest
    outputs:
      version: ${{ steps.parse_branch.outputs.version }}
      version_tag: ${{ steps.parse_branch.outputs.version_tag }}
      branch_name: ${{ env.BRANCH_NAME }}
    
    steps:

      - name: Parse branch name
        id: parse_branch
        run: |
          set -euo pipefail
          
          release_version="$(<<<"$BRANCH_NAME" sed 's/.*\///')"
          if [[ -z "$release_version" ]]; then
            echo "::error title=invalid branch name::$BRANCH_NAME isn't \"*/<version>\""
            exit 1
          fi
          set -x
          echo "version=$release_version" >> "$GITHUB_OUTPUT"
          echo "version_tag=v$release_version" >> "$GITHUB_OUTPUT"

      - uses: actions/checkout@v4
        with:
          ref: ${{ env.BRANCH_NAME }}
          fetch-depth: '50' # all we really need is >1, but this can give us additional context

      - name: Verify release
        run: gh release view "$VERSION_TAG"
        env:
          VERSION_TAG: ${{ steps.parse_branch.outputs.version_tag }}

      - name: Get target branch name from PR if needed
        if: ${{ ! env.TARGET_BRANCH }}
        run: |
          set -euo pipefail
          target_name="$(gh pr view "$BRANCH_NAME" --json baseRefName | jq -r .baseRefName)"
          echo "TARGET_BRANCH=$target_name" >> "$GITHUB_ENV"

      - name: Fetch target branch
        run: git fetch origin "$TARGET_BRANCH"

      - name: Check for exactly one commit
        run: |
          set -euo pipefail
          
          commits_between="$(git log "origin/$TARGET_BRANCH..HEAD" --pretty=format:"%H")"
          
          if [[ "$(wc -l <<< "$commits_between")" -ne 1 ]]; then
            echo "::error title=invalid branch state::require exactly commit between $TARGET_BRANCH and $BRANCH_NAME: found $(wc -l <<<"$commits_between")"
            exit 1
          fi

  build:
    needs: validate
    uses: ./.github/workflows/build-release.yml
    secrets: inherit
    with:
      docker-tag-type: rc
      branch_name: ${{ needs.validate.outputs.branch_name }}

  upload:
    needs: [ build, validate ]
    runs-on: ubuntu-latest
    env:
      GH_TOKEN: ${{ github.token }}
    steps:

      - name: "Download binaries from job"
        id: download
        uses: actions/download-artifact@v4
        with:
          pattern: mdq-*
          merge-multiple: 'false'
          path: from_build

      - name: "Zip up the files"
        run: |
          set -euo pipefail
          mkdir to_upload
          to_upload="$PWD/to_upload"
          set -x
          for f in $(cd "$DOWNLOAD_DIR"; ls -1) ; do
            pushd "from_build/$f"
            if [[ -e mdq ]]; then
              chmod +x mdq
            fi
            if [[ "$f" == *-windows-* ]]; then
              zip -r "$f.zip" *
              mv "$f.zip" "$to_upload"
            else
              tar -czvf "$f.tar.gz" *
              mv "$f.tar.gz" "$to_upload"
            fi
            popd
          done
        env:
          DOWNLOAD_DIR: ${{ steps.download.outputs.download-path }}

      - name: "Upload binaries to release"
        run:
          gh release -R "$REPO_NAME" upload --clobber "$VERSION_TAG" to_upload/*
        env:
          REPO_NAME: ${{ github.repository }}
          VERSION_TAG: ${{ needs.validate.outputs.version_tag }}

  verify-binaries:
    needs: [ validate, upload ]
    permissions:
      contents: write
    uses: ./.github/workflows/binary-verify.yml
    with:
      version: ${{ needs.validate.outputs.version }}
      docker-rc: true # since they won't have been published yet
