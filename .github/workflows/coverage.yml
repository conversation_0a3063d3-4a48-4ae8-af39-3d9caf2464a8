name: Coverage

on:
  push:
    branches: [ "main" ]

jobs:
  coverage:
    runs-on: ubuntu-latest
    environment: Code Coverage Badge
    env:
      CARGO_TERM_COLOR: always
    steps:

      - uses: actions/checkout@v4

      - name: install cargo-tarpaulin
        uses: taiki-e/install-action@v2
        with:
          tool: cargo-tarpaulin

      - name: Generate code coverage
        run: |
          cargo tarpaulin -o json --output-dir target/
          percent_coverage="$(cat target/tarpaulin-report.json | jq .coverage)"
          printf '::notice title=Coverage (lines %%)::%s' "$percent_coverage"
          echo "LINES_PERCENT=$percent_coverage" >> "$GITHUB_ENV"
        env:
          TEST_TIMEOUT_MULTIPLIER: 10

      - name: Upload to gist
        run: ./scripts/percent_to_shields_gist coverage "$LINES_PERCENT"
        env:
          GIST_URL: ${{ vars.COVERAGE_GIST_URL }}
          GH_TOKEN: ${{ secrets.API_TOKEN }}

      # Note: the following could technically be a separate job, but the "environment"
      # bit means it counts as a deployment, not just a normal build. It's nice to have
      # just one deployment per job run, so I'm keeping this all in one job.
      # It's pretty quick, anyway.
      - name: Count TODOs
        run: |
          set -euo pipefail
          todos_count="$(
            git ls-tree -r --name-only HEAD |
            grep --fixed-string -v .github/workflows/coverage.yml |
            grep --fixed-string -v README.md |
            (xargs grep todo -i -o || true) |
            wc -l
          )"
          if [ "$todos_count" -eq 0 ]; then
            color=green
          else
            color=orange
          fi
          json_text="$(echo '{}' | 
            jq -c '{schemaVersion: 1, label: $badge_label, color: $color, message: $message}' \
            --arg badge_label "Pending TODOs" --arg color "$color" --arg message "$todos_count")"

          printf '::notice title=TODOs Count::%s' "$todos_count"
          gh gist edit "$GIST_URL" <(echo "$json_text")
        env:
          GIST_URL: ${{ vars.TODOS_GIST_URL }}
          GH_TOKEN: ${{ secrets.API_TOKEN }}

      - name: Count Ignored Tests
        run: |
          rs_ignored="$(find . -not -path './target/*' -name '*.rs' -exec grep --fixed-strings -Hno '#[ignore]' {} \; | wc -l)"
          integ_ignored="$(cd tests/md_cases/ ; find . -name '*.toml' -exec grep -Hno '^ignore\b' {} \; | wc -l)"
          total_ignored="$(( $rs_ignored + $integ_ignored ))"

          if [ "$total_ignored" -eq 0 ]; then
            color=green
          else
            color=orange
          fi
          json_text="$(echo '{}' | 
            jq -c '{schemaVersion: 1, label: $badge_label, color: $color, message: $message}' \
            --arg badge_label "Ignored Tests" --arg color "$color" --arg message "$total_ignored")"

          printf '::notice title=Ignored Tests Count::%s' "$total_ignored"
          gh gist edit "$GIST_URL" <(echo "$json_text")
        env:
          GIST_URL: ${{ vars.IGNOREDS_GIST_URL }}
          GH_TOKEN: ${{ secrets.API_TOKEN }}
