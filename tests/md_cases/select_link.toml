[given]
md = '''
This text has both [an inline link](https://example.com/inline) and a [referenced link][1]. It also has an image:

![image alt text](https://example.com/hylas-and-nymphs.png)

[1]: https://example.com/referenced
'''


[expect."select all"]
cli_args = ['[]()', '--link-format=inline']
output = '''
[an inline link](https://example.com/inline)

   -----

[referenced link](https://example.com/referenced)'''


[expect."select link by text"]
cli_args = ['[an inline]()', '--link-format=inline']
output = '''
[an inline link](https://example.com/inline)'''


[expect."select link by url"]
cli_args = ['[]("/referenced")', '--link-format=inline']
output = '''
[referenced link](https://example.com/referenced)'''


[expect."select image"]
cli_args = ['![]()', '--link-format=inline']
output = '''
![image alt text](https://example.com/hylas-and-nymphs.png)'''


[expect."chained"]
cli_args = ['[inline]() | [](example.com)']
output = '''
[an inline link][1]

[1]: https://example.com/inline
'''
