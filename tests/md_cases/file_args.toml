[given]
md = '''
- from stdin
'''
files."one.txt" = '''
- from one.txt
'''
files."two.txt" = '''
- from two.txt
'''

[chained]
needed = false

[expect."read one file"]
cli_args = ['-', '-oplain', 'one.txt']
output = '''
from one.txt
'''

[expect."read two files"]
cli_args = ['-', '-oplain', 'one.txt', 'two.txt']
output = '''
from one.txt
from two.txt
'''

[expect."read a file twice"]
cli_args = ['-', '-oplain', 'one.txt', 'one.txt']
output = '''
from one.txt
from one.txt
'''

[expect."explicitly read stdin"]
cli_args = ['-', '-oplain', '-']
output = '''
from stdin
'''

[expect."explicitly read stdin twice"] # will only read it once!
cli_args = ['-', '-oplain', '-', '-']
output = '''
from stdin
'''

[expect."file is missing"] # will only read it once!
cli_args = ['-', '-oplain', 'missing-err.txt']
expect_success = false
output = ''
output_err = '''entity not found while reading file "missing-err.txt"
'''
