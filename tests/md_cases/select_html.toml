[given]
md = '''
Text with <span>inline html</span>.

<div
class="block>

and a div block

</div>
'''


[expect."select all"]
cli_args = ['</>']
output = '''
<span>

   -----

</span>

   -----

<div
class="block>

   -----

</div>
'''


[expect."inline with matcher"]
cli_args = ['</> span']
output = '''
<span>

   -----

</span>
'''


[expect."inline with matcher tag"]
cli_args = ['</> "<span>"']
output = '''
<span>
'''


[expect."unquoted tag"]
cli_args = ['</> <span>']
expect_success = false # unquoted string must start with a letter, not a '<'
output = ''
output_err = '''Syntax error in select specifier:
 --> 1:5
  |
1 | </> <span>
  |     ^---
  |
  = expected end of input, "*", unquoted string, regex, quoted string, or "^"
'''


[expect."block with matcher"]
cli_args = ['</> class']
output = '''
<div
class="block>
'''


[expect."chained"]
cli_args = ['</> span | </> "<span>"']
output = '''
<span>
'''