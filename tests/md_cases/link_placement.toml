[given]
md = '''
# First Section

This is [an interesting link][1].

# Second section

Some section text with [another link][2].

[1]: https://example.com/interesting
[2]: https://example.com/another
'''

[chained]
needed = false


[expect."standard link placement"]
cli_args = []
output = '''
# First Section

This is [an interesting link][1].

[1]: https://example.com/interesting

# Second section

Some section text with [another link][2].

[2]: https://example.com/another
'''


[expect."links at bottom of doc"]
cli_args = ["--link-pos", "doc"]
output = '''
# First Section

This is [an interesting link][1].

# Second section

Some section text with [another link][2].

[1]: https://example.com/interesting
[2]: https://example.com/another
'''


[expect."separate links positioned by section"]
cli_args = ["[]()", "--link-pos", "section"]
output = '''
[an interesting link][1]

[1]: https://example.com/interesting

   -----

[another link][2]

[2]: https://example.com/another
'''

[expect."separate links positioned by doc"]
cli_args = ["[]()", "--link-pos", "doc"]
output = '''
[an interesting link][1]

   -----

[another link][2]

   -----

[1]: https://example.com/interesting
[2]: https://example.com/another
'''
