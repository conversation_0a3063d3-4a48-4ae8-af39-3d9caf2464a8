[given]
md = '''
# Original Title

This is a paragraph with text.

```original-language
original code content here
```

Here are some list items:

- Item with **bold text** formatting
- Item with _emphasis and **nested bold**_ formatting
- ![original image alt](https://example.com/original/image.png) description
- [original link text](https://example.com/original/page.html) description
'''

[chained]
needed = false

[expect."search-replace section title"]
cli_args = ['--link-format', 'keep', '# !s/Original/New/']
output = '''
# New Title

This is a paragraph with text.

```original-language
original code content here
```

Here are some list items:

- Item with **bold text** formatting
- Item with _emphasis and **nested bold**_ formatting
- ![original image alt](https://example.com/original/image.png) description
- [original link text](https://example.com/original/page.html) description
'''

[expect."search-replace code block language"]
cli_args = ['``` !s/original-.*/python/']
expect_success = false
output = ''
output_err = '''Selection error:
code block selector does not support string replace
'''

[expect."search-replace code block contents"]
cli_args = ['``` !s/original/new/']
expect_success = false
output = ''
output_err = '''Selection error:
code block selector does not support string replace
'''

[expect."search-replace image alt text"]
cli_args = ['![ !s/original/new/ ]()']
expect_success = false
output = ''
output_err = '''Selection error:
image selector does not support string replace
'''

[expect."search-replace image alt text with non-matching"]
# Even though there's no match, it should still error. The error shouldn't have anything to do with the target Markdown.
cli_args = ['![ !s/BOGUS/new/ ]()']
expect_success = false
output = ''
output_err = '''Selection error:
image selector does not support string replace
'''

[expect."search-replace image url"]
cli_args = ['![](!s/original/new/)']
output = '''![original image alt][1]

[1]: https://example.com/new/image.png
'''

[expect."search-replace link text"]
cli_args = ['[ !s/original/new/ ]()']
expect_success = false
output = ''
output_err = '''Selection error:
hyperlink selector does not support string replace
'''

[expect."search-replace link url"]
cli_args = ['[]( !s/original/new/ )']
output = '''[original link text][1]

[1]: https://example.com/new/page.html
'''

[expect."search-replace straightforward formatting"]
cli_args = ['- !s/bold/strong/']
expect_success = false
output = ''
output_err = '''Selection error:
list item selector does not support string replace
'''

[expect."search-replace nested formatting"]
cli_args = ['- !s/and nested/and formerly/']
expect_success = false
output = ''
output_err = '''Selection error:
list item selector does not support string replace
'''

[expect."search-replace paragraph text"]
cli_args = ['P: !s/paragraph/text/']
expect_success = false
output = ''
output_err = '''Selection error:
paragraph selector does not support string replace
'''
