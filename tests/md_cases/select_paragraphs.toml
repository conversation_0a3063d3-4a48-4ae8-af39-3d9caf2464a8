[given]
md = '''
Hello, world.

> Paragraph within a block quote.

This paragraph has _inline_ **formatting**.
'''


[expect."all"]
cli_args = ["P:"]
output = '''
Hello, world.

   -----

Paragraph within a block quote.

   -----

This paragraph has _inline_ **formatting**.
'''


[expect."all but with explicit all-matcher"]
cli_args = ["P: *"]
output = '''
Hello, world.

   -----

Paragraph within a block quote.

   -----

This paragraph has _inline_ **formatting**.
'''


[expect."select within a block quote"]
cli_args = ["P: block"]
output = '''
Paragraph within a block quote.
'''


[expect."matcher ignores inline formatting"]
cli_args = ["P: has inline"]
# The markdown is "has _inline_", but the emphasis formatting is ignored for matching. It's still used for output.
output = '''
This paragraph has _inline_ **formatting**.
'''


[expect."no colon after p"]
cli_args = ["P *"]
expect_success = false
output = ''
output_err = '''Syntax error in select specifier:
 --> 1:1
  |
1 | P *
  | ^---
  |
  = expected valid query
'''


[expect."space before colon"]
cli_args = ["P : *"]
expect_success = false
output = ''
output_err = '''Syntax error in select specifier:
 --> 1:1
  |
1 | P : *
  | ^---
  |
  = expected valid query
'''


[expect."chained"]
cli_args = ['P: hello | P: world']
output = '''
Hello, world.
'''