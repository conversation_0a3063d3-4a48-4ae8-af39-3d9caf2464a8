[given]
md = '''
Are you ready for a table?

| Name | Description |
|:----:|-------------|
| Foo  | Not a fizz  |
| Bar  | Not a buzz  |
| Barn | Big, red.   | And this is an extra column |
| Fuzz |

Note that the "Barn" row has an extra column, and the "Fuzz" row is missing one.
'''


[expect."table not normalized by default"]
cli_args = [""]
output = '''
Are you ready for a table?

| Name | Description |
|:----:|-------------|
| Foo  | Not a fizz  |
| Bar  | Not a buzz  |
| Barn | Big, red.   | And this is an extra column |
| Fuzz |

Note that the "Barn" row has an extra column, and the "Fuzz" row is missing one.
'''


[expect."select all table cells normalizes"]
cli_args = [":-: * :-:"]
output = '''
| Name | Description |                             |
|:----:|-------------|-----------------------------|
| Foo  | Not a fizz  |                             |
| Bar  | Not a buzz  |                             |
| Barn | Big, red.   | And this is an extra column |
| Fuzz |             |                             |'''


[expect."select only name"]
# note: "Name" has an 'a', "Description" doesn't. There are other rows that do contain 'a' in the Description column,
# but the first matcher only checks the header cells (by design).
cli_args = [":-: a :-:"]
output = '''
| Name |
|:----:|
| Foo  |
| Bar  |
| Barn |
| Fuzz |'''


[expect."select only description"]
cli_args = [":-: description :-:"]
output = '''
| Description |
|-------------|
| Not a fizz  |
| Not a buzz  |
| Big, red.   |
|             |'''


[expect."select only description by regex"]
cli_args = [":-: /Description/ :-:"]
output = '''
| Description |
|-------------|
| Not a fizz  |
| Not a buzz  |
| Big, red.   |
|             |'''


[expect."select only the big red row"]
# Note: header row always survives
cli_args = [":-: * :-: 'Big, red' "]
output = '''
| Name | Description |                             |
|:----:|-------------|-----------------------------|
| Barn | Big, red.   | And this is an extra column |'''


[expect."chained"]
cli_args = [":-: * :-: * | :-: * :-: * | "]
output = '''
| Name | Description |                             |
|:----:|-------------|-----------------------------|
| Foo  | Not a fizz  |                             |
| Bar  | Not a buzz  |                             |
| Barn | Big, red.   | And this is an extra column |
| Fuzz |             |                             |'''

[expect."output plain"]
cli_args = ["-o", "plain", ":-: * :-: *"]
output = '''
Name Description
Foo Not a fizz
Bar Not a buzz
Barn Big, red. And this is an extra column
Fuzz
'''
