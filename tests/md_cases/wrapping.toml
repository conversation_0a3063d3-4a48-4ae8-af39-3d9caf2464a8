[given]
#        1         2         3         4         5
# 345678901234567890123456789012345678901234567890
md = '''
This text has both [an inline link](https://example.com/inline) and a [referenced link][1]. It also has an image:

![image alt text](https://example.com/hylas-and-nymphs.png)

[1]: https://example.com/referenced

> We can also have text that wraps within quoted text.

- Or we can have text that wraps within ordered lists
  - including sublists, which should take the indentation into account
  - and even if there are multiple items that are in there
'''
#        1         2         3         4         5
# 345678901234567890123456789012345678901234567890

[chained]
needed = false


[expect."wrapping at 50"]
cli_args = ['--wrap-width=50', '--link-format=keep']
output = '''
This text has both
[an inline link](https://example.com/inline) and a
[referenced link][1]. It also has an image:

![image alt text](https://example.com/hylas-and-nymphs.png)

> We can also have text that wraps within quoted
> text.

- Or we can have text that wraps within ordered
  lists

  - including sublists, which should take the
    indentation into account
  - and even if there are multiple items that are
    in there

[1]: https://example.com/referenced
'''
